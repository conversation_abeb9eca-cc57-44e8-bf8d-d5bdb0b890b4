import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.math.BigInteger;
import java.util.*;

public class Main {
    static int f1, f2, f3, f4, f5, f6, f[] = new int[155], lj[] = new int[150], n, m, sl, h, l, cd = 100005;
    static int ydh[] = { -1, 0, 1, 0 }, ydl[] = { 0, 1, 0, -1 };
    static long jg, ans, sum, pre, a[] = new long[cd], b[], c[], d[];

    static void pd() {
        for (int i = 0; i < sl; i++) {
            print(lj[i + 1] + " ");
        }
        println();
    }

    static void dfs(int x) {
        if (x == sl + 1) {
            pd();
            return;
        }
        for (int i = 1; i <= sl; i++) {
            if (f[i] == 0) {
                f[i] = 1;
                lj[x] = i;
                dfs(x + 1);
                f[i] = 0;
            }
        }
    }

    public static List<List<String>> groupAnagrams(String[] strs) {
        HashMap<String,Integer> hs=new HashMap<>();
        HashMap<Integer,Integer>hs2=new HashMap<>();
        int l=strs.length;
        List<List<String>> ans=new ArrayList<>();
        int x=0;
        for (int i = 0; i < l; i++) {
            char a[] = strs[i].toCharArray();
            Arrays.sort(a);
            String b=new String(a);
            if(hs.containsKey(b)){
                ans.get(hs2.get(hs.get(b))).add(strs[i]);
            }
            else {
                hs.put(b, i);
                hs2.put(i, x);
                ans.add(new ArrayList<>());
                ans.get(x).add(strs[i]);
                x++;
            }
        }
        return ans;
    }
    static void solve() throws NumberFormatException, IOException {
        groupAnagrams(new String[]{"eat", "tea", "tan", "ate", "nat", "bat"});

    }

    public static void main(String[] args) throws NumberFormatException, IOException {
        // TODO Auto-generated method stub
        int t = 1;
        while (t-- > 0) {
            solve();
        }
        flush();
    }

    static BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
    static StringTokenizer sk;
    static PrintWriter pw = new PrintWriter(new BufferedOutputStream(System.out));

    static boolean hasnext() throws IOException {
        if (sk != null && sk.hasMoreElements())
            return true;
        br.mark(1024);
        String s = br.readLine();
        br.reset();
        return sk != null && !s.isEmpty();
    }

    static String next() throws IOException {
        while (sk == null || !sk.hasMoreElements()) {
            String s = br.readLine();
            sk = new StringTokenizer(s);
        }
        return sk.nextToken();
    }

    static int nextint() throws NumberFormatException, IOException {
        return Integer.parseInt(next());
    }

    static long nextlong() throws NumberFormatException, IOException {
        return Long.parseLong(next());
    }

    static double nextdouble() throws NumberFormatException, IOException {
        return Double.parseDouble(next());
    }

    static BigInteger nextbig() throws NumberFormatException, IOException {
        return new BigInteger(next());
    }

    static void print(Object o) {
        pw.print(o);
    }

    static void println(Object o) {
        pw.println(o);
    }

    static void println() {
        pw.println();
    }

    static void printf(String s, Object... o) {
        pw.printf(s, o);
    }

    static void flush() {
        pw.flush();
    }
}
