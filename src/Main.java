import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.math.BigInteger;
import java.util.*;

public class Main {
    static int f1, f2, f3, f4, f5, f6, f[] = new int[155], lj[] = new int[150], n, m, sl, h, l, cd = 100005;
    static int ydh[] = {-1, 0, 1, 0}, ydl[] = {0, 1, 0, -1};
    static long jg, ans, sum, pre, a[] = new long[cd], b[], c[], d[];
    static BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
    static StringTokenizer sk;
    static PrintWriter pw = new PrintWriter(new BufferedOutputStream(System.out));

    static void pd() {
        for (int i = 0; i < sl; i++) {
            print(lj[i + 1] + " ");
        }
        println();
    }

    static void dfs(int x) {
        if (x == sl + 1) {
            pd();
            return;
        }
        for (int i = 1; i <= sl; i++) {
            if (f[i] == 0) {
                f[i] = 1;
                lj[x] = i;
                dfs(x + 1);
                f[i] = 0;
            }
        }
    }

    public static List<List<Integer>> threeSum(int[] nums) {
        int l1 = nums.length;
        HashSet<List<Integer>> jg = new HashSet<>();

        int zeroCount = 0;
        for (int num : nums) {
            if (num == 0) {
                zeroCount++;
            }
        }
        if (zeroCount == l1) {
            List<Integer> x = new ArrayList();
            List<List<Integer>> sss = new ArrayList<>();
            x.add(0);
            x.add(0);
            x.add(0);
            sss.add(x);
            return sss;
        }

        for (int i1 = 0; i1 < l1; i1++) {
            int target = -nums[i1];
            HashMap<Integer, Integer> map = new HashMap<>();
            for (int i = i1 + 1; i < l1; i++) {
                if (map.containsKey(nums[i])) {
                    Integer sz[] = {nums[i1], nums[i], nums[map.get(nums[i])]};
                    Arrays.sort(sz);
                    List<Integer> list = Arrays.asList(sz);
                    jg.add(list);
                }
                map.put(target - nums[i], i);
            }
        }
        List<List<Integer>> res = new ArrayList<>();
        for (List<Integer> a : jg) {
            res.add(new ArrayList<>(a));
        }
        return res;
    }

    public static List<Integer> findAnagrams(String s, String p) {
        List<Integer> res = new ArrayList<>();
        TreeMap<Character, Integer> map = new TreeMap<>();
        TreeMap<Character, Integer> map2 = new TreeMap<>();
        for (char c : p.toCharArray()) {
            map.put(c, map.getOrDefault(c, 0) + 1);
        }
        int l = p.length();
        int l1 = s.length();
        for (int i = 0; i < l; i++) {
            map2.put(s.charAt(i), map2.getOrDefault(s.charAt(i), 0) + 1);
        }
        for (int i = l - 1; i < l1; i++) {
            if (i != l - 1) {
                map2.put(s.charAt(i - l), map2.get(s.charAt(i-l)) - 1);
                if(map2.get(s.charAt(i-l)) <= 0){
                    map2.remove(s.charAt(i-l));
                }
                map2.put(s.charAt(i), map2.getOrDefault(s.charAt(i), 0) + 1);
            }
            System.out.printf("%s %s\n", map.toString(), map2.toString());
            if (map.toString().equals(map2.toString())) {
                res.add(i - l + 1);
            }
        }
        return res;
    }
    static void solve() throws NumberFormatException, IOException {
        System.out.println( findAnagrams("dinitrophenylhydrazinetrinitrophenylmethylnitramine"
               , "trinitrophenylmethylnitramine"));
    }

    public static void main(String[] args) throws NumberFormatException, IOException {
        // TODO Auto-generated method stub
        int t = 1;
        while (t-- > 0) {
            solve();
        }
        flush();
    }

    static boolean hasnext() throws IOException {
        if (sk != null && sk.hasMoreElements())
            return true;
        br.mark(1024);
        String s = br.readLine();
        br.reset();
        return sk != null && !s.isEmpty();
    }

    static String next() throws IOException {
        while (sk == null || !sk.hasMoreElements()) {
            String s = br.readLine();
            sk = new StringTokenizer(s);
        }
        return sk.nextToken();
    }

    static int nextint() throws NumberFormatException, IOException {
        return Integer.parseInt(next());
    }

    static long nextlong() throws NumberFormatException, IOException {
        return Long.parseLong(next());
    }

    static double nextdouble() throws NumberFormatException, IOException {
        return Double.parseDouble(next());
    }

    static BigInteger nextbig() throws NumberFormatException, IOException {
        return new BigInteger(next());
    }

    static void print(Object o) {
        pw.print(o);
    }

    static void println(Object o) {
        pw.println(o);
    }

    static void println() {
        pw.println();
    }

    static void printf(String s, Object... o) {
        pw.printf(s, o);
    }

    static void flush() {
        pw.flush();
    }
}
