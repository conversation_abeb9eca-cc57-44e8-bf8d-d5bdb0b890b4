import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.math.BigInteger;
import java.util.*;

public class Main {
    static int f1, f2, f3, f4, f5, f6, f[] = new int[155], lj[] = new int[150], n, m, sl, h, l, cd = 100005;
    static int ydh[] = {-1, 0, 1, 0}, ydl[] = {0, 1, 0, -1};
    static long jg, ans, sum, pre, a[] = new long[cd], b[], c[], d[];
    static BufferedReader br = new BufferedReader(new InputStreamReader(System.in));
    static StringTokenizer sk;
    static PrintWriter pw = new PrintWriter(new BufferedOutputStream(System.out));

    static void pd() {
        for (int i = 0; i < sl; i++) {
            print(lj[i + 1] + " ");
        }
        println();
    }

    static void dfs(int x) {
        if (x == sl + 1) {
            pd();
            return;
        }
        for (int i = 1; i <= sl; i++) {
            if (f[i] == 0) {
                f[i] = 1;
                lj[x] = i;
                dfs(x + 1);
                f[i] = 0;
            }
        }
    }

    public static List<List<Integer>> threeSum(int[] nums) {
        int l1 = nums.length;
        HashSet<List<Integer>> jg = new HashSet<>();

        int zeroCount = 0;
        for (int num : nums) {
            if (num == 0) {
                zeroCount++;
            }
        }
        if (zeroCount == l1) {
            List<Integer> x = new ArrayList();
            List<List<Integer>> sss = new ArrayList<>();
            x.add(0);
            x.add(0);
            x.add(0);
            sss.add(x);
            return sss;
        }

        for (int i1 = 0; i1 < l1; i1++) {
            int target = -nums[i1];
            HashMap<Integer, Integer> map = new HashMap<>();
            for (int i = i1 + 1; i < l1; i++) {
                if (map.containsKey(nums[i])) {
                    Integer sz[] = {nums[i1], nums[i], nums[map.get(nums[i])]};
                    Arrays.sort(sz);
                    List<Integer> list = Arrays.asList(sz);
                    jg.add(list);
                }
                map.put(target - nums[i], i);
            }
        }
        List<List<Integer>> res = new ArrayList<>();
        for (List<Integer> a : jg) {
            res.add(new ArrayList<>(a));
        }
        return res;
    }

    public static int trap(int[] height) {
        int n = height.length, jg = 0, l = 0, r = 1;
        for (int i = 0; i < n; i++) {
            if (height[i] != 0) {
                l = i;
                r = i + 1;
                break;
            }
        }
        int sum = 0;
        while (r < n) {
            if (height[l] <= height[r]) {
                jg += sum;
                sum = 0;
                l = r;
            }
            sum += height[l] - height[r];
            r++;
        }
        if (l != n - 1) {
            int l1 = n - 1, r1 = n - 1;
            for (int i = n - 1; i >= l; i--) {
                if (height[i] != 0) {
                    l1 = i;
                    r1 = i - 1;
                    break;
                }
            }
            int sum1 = 0;
            while (r1 >= l) {
                if (height[l1] <= height[r1]) {
                    jg += sum1;
                    sum1 = 0;
                    l1 = r1;
                }
                sum1 += height[l1] - height[r1];
                r1--;
            }
        }
        return jg;
    }

    static void solve() throws NumberFormatException, IOException {
        System.out.println(trap(new int[]{0, 1, 0, 2, 1, 0, 1, 3, 2, 1, 2, 1}));
    }

    public static void main(String[] args) throws NumberFormatException, IOException {
        // TODO Auto-generated method stub
        int t = 1;
        while (t-- > 0) {
            solve();
        }
        flush();
    }

    static boolean hasnext() throws IOException {
        if (sk != null && sk.hasMoreElements())
            return true;
        br.mark(1024);
        String s = br.readLine();
        br.reset();
        return sk != null && !s.isEmpty();
    }

    static String next() throws IOException {
        while (sk == null || !sk.hasMoreElements()) {
            String s = br.readLine();
            sk = new StringTokenizer(s);
        }
        return sk.nextToken();
    }

    static int nextint() throws NumberFormatException, IOException {
        return Integer.parseInt(next());
    }

    static long nextlong() throws NumberFormatException, IOException {
        return Long.parseLong(next());
    }

    static double nextdouble() throws NumberFormatException, IOException {
        return Double.parseDouble(next());
    }

    static BigInteger nextbig() throws NumberFormatException, IOException {
        return new BigInteger(next());
    }

    static void print(Object o) {
        pw.print(o);
    }

    static void println(Object o) {
        pw.println(o);
    }

    static void println() {
        pw.println();
    }

    static void printf(String s, Object... o) {
        pw.printf(s, o);
    }

    static void flush() {
        pw.flush();
    }
}
